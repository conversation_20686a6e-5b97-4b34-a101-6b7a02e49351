<template>
  <div class="_fw">
    <q-input
        @keyup.enter="emit('enter')"
        @focus="toggleFocus(true)"
        @blur="toggleFocus(false)"
        @update:model-value="handleInput($event)"
        :model-value="input"
        v-bind="{
          disable,
          noErrorIcon: true,
          placeholder: 'Phone Number',
          class: '_fw',
          error: hasBlurred ? input.length && !valid : undefined,
           ...inputAttrs
        }"
    >
      <template v-slot:prepend>
        <slot name="prepend">
          <div class="flex flex-center">
            <div>
              <span style="font-size: 20px">{{ getFlag(_get(country, 'iso2', 'us'), 'emoji') }}</span>
              <q-popup-proxy>
                <q-list separator>
                  <q-item>
                    <q-input borderless v-model="search" placeholder="Search" dense>
                      <template v-slot:prepend>
                        <q-icon name="mdi-magnify"></q-icon>
                      </template>
                    </q-input>
                  </q-item>
                  <template v-if="!search">
                    <country-item
                        v-for="(c, i) in favorites"
                        :key="`c-f-${i}`"
                        :active="country.iso2 === c.iso2"
                        clickable
                        @click="country = Object.assign({}, c)">
                    </country-item>
                  </template>
                  <template v-if="search">
                    <country-item
                        v-for="(c, i) in countries"
                        :key="`c-${i}`"
                        :active="country.iso2 === c.iso2"
                        clickable
                        @click="country = Object.assign({}, c)">
                    </country-item>
                  </template>
                </q-list>
              </q-popup-proxy>
            </div>
          </div>
        </slot>
      </template>

      <template v-slot:append>
        <slot name="append"></slot>
      </template>
      <template v-slot:after>
        <slot name="after"></slot>
      </template>

    </q-input>
  </div>
</template>

<script setup>
  import CountryItem from './CountryItem.vue';

  import {_get} from 'symbol-syntax-utils';
  import {computed, ref, watch} from 'vue';
  import {phoneInput} from 'components/common/phone/phone-input';

  const emit = defineEmits(['focus', 'blur', 'update:input', 'update:model-value', 'update:valid', 'update:full', 'enter'])
  const props = defineProps({
    iconMode: Boolean,
    icon: Object,
    disable:Boolean,
    preferredCountries: {
      type: Array,
      default: () => ['US']
    },
    emitValue: Boolean,
    optionValue: { type: String, default: 'number.e164' },
    inputAttrs: Object,
    modelValue: { type: [Object, String, Number] },
    countryOptions: Object,
    displayPath: { type: String, default: 'number.national' }
  })

  const preferred = computed(() => props.preferredCountries);
  const {
    input,
    country,
    search,
    countries,
    favorites,
    handleCountryCode,
    handleInput,
    getFlag,
    valid
  } = phoneInput({ emit, preferredCountries: preferred, emitValue: props.emitValue, optionValue: props.optionValue })

  const focus = ref(false);
  const hasBlurred = ref(false);

  const toggleFocus = (val) => {
    focus.value = val;
    if (val) emit('focus');
    else {
      hasBlurred.value = true;
      emit('blur')
    }
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) {
      let v = nv
      if (typeof nv === 'object') {
        v = _get(nv, [props.displayPath], _get(nv, 'number.input'))
      }
      if (v !== input.value) handleInput(v);
    }
  }, { immediate: true });

  watch(() => props.countryOptions, (nv, ov) => {
    if (nv && JSON.stringify(nv) === JSON.stringify(ov)) {
      handleCountryCode(nv, false)
    }
  }, { immediate: true });
</script>

<style scoped lang="scss">

</style>
