<template>
  <q-input
      @blur="blurWatch"
      @update:model-value="handleInput"
      @focus="hasFocused = true"
      v-bind="{
        disable,
    error: hasBlurred ? input?.length > 5 ? !valid : false : false,
    errorMessage: 'Enter a valid email',
    modelValue: input,
        ...attrs,
      }"
  >

    <template v-slot:prepend v-if="icon">
      <q-icon v-bind="icon"></q-icon>
    </template>
    <template v-slot:append>
      <slot name="append"></slot>
    </template>
  </q-input>
</template>

<script setup>
  import {useAttrs, ref, watch, computed, nextTick} from 'vue';

  const input = ref('');

  const emit = defineEmits(['update:model-value', 'update:valid', 'input', 'blur-value', 'blur']);
  const attrs = useAttrs();
  const props = defineProps({
    disable: Boolean,
    modelValue: String,
    icon: {
      type: Object, default: () => {
        return {
          name: 'mdi-email',
          color: 'primary',
          size: '20px'
        }
      }
    }
  });

  const isEmailRule = (val) => {
    const reg = /^(([^<>()[\]\\.,;:\s@']+(\.[^<>()[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,24}))$/;
    return reg.test(val);
  };

  const valid = ref(false);
  const hasFocused = ref(false);
  const handleInput = (val, pause) => {
    input.value = val;
    valid.value = isEmailRule(val);
    emit('update:valid', valid.value);
    emit('input', val?.toLowerCase().trim())
    if(!pause && valid.value) {
      emit('update:model-value', val.toLowerCase().trim());
    }
  };

  const hasBlurred = ref(false);
  const blurWatch = () => {
    emit('blur')
    hasBlurred.value = true;
    if(valid.value) {
      emit('blur-value', input.value);
      nextTick(() => {
        if(!props.modelValue) input.value = '';
      })
    }
  }

  const mv = computed(() => props.modelValue);

  watch(mv, (newVal) => {
    if (newVal !== input.value) handleInput(newVal, true);
  }, { immediate: true});

</script>

<style scoped>

</style>
