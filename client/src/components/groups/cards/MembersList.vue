<template>
  <div class="_fw">
    <div class="row items-center q-py-sm">
      <q-input style="width: 400px; max-width: 80%" v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>
      <q-space></q-space>
      <q-btn flat icon-right="mdi-plus" :label="$q.screen.lt.md ? '' : 'Add Members'" @click="addDialog = true">
        <q-tooltip>Add Member</q-tooltip>
      </q-btn>
    </div>
    <div class="_fw __tc">
      <q-table
          :rows-per-page-options="[0]"
          flat
          :columns="columns"
          :rows="h$.data.map(a => a._fastjoin?.person)"
          hide-no-data
          hide-bottom
          hide-pagination
      >
        <template v-slot:header="scope">
          <!--        <q-th auto-width></q-th>-->
          <q-th
              v-for="col in scope.cols"
              :key="col.name"
              :props="scope"
          >
            {{ col.label }}
          </q-th>
        </template>
        <template v-slot:body="scope">
          <q-tr :props="scope">
            <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
              <component
                  v-if="col.component"
                  :is="col.component"
                  v-bind="col.attrs(scope.row)"
              ></component>
              <div v-else>{{ col.value }}</div>
            </q-td>
          </q-tr>
        </template>
      </q-table>
      <pagination-row v-bind="{ h$, pagination, pageRecordCount }"></pagination-row>
    </div>

    <common-dialog v-model="addDialog" setting="standard" :dialog-attrs="{ maximized: $q.screen.lt.md }">
      <div class="__dc">
        <add-member  :org="item.org" :model-value="item"></add-member>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import TdChip from 'src/components/common/tables/TdChip.vue';
  import DefaultChip from 'src/components/common/avatars/DefaultChip.vue';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import AddMember from 'src/components/groups/forms/AddMember.vue';
  import MemberEditButton from 'src/components/groups/utils/MemberEditButton.vue';
  import CamsChip from 'src/components/comps/cams/cards/CamsChip.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';

  import {ref, computed} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery'
  import {useGroups} from 'src/stores/groups';

  import {_get} from 'symbol-syntax-utils';
  import {useGrpMbrs} from 'stores/grp-mbrs';

  const store = useGroups();
  const mbrStore = useGrpMbrs();

  const props = defineProps({
    modelValue: { required: true }
  });

  const search = ref({
    text: '',
    keys: ['name', 'email', 'phone.number.e164']
  })

  const value = computed(() => props.modelValue);
  const { item } = idGet({
    store,
    value
  })


  const { searchQ } = HQuery({
    search
  })

  const params = computed(() => {
    return {
      runJoin: { with_person: true },
      query: {
        ...searchQ.value,
        $sort: { name: 1 },
        group: item.value._id
      }

      // _limit_to: {
      //   service: 'members',
      //   params: {
      //     query: {
      //       org: item.value?.org
      //     }
      //   },
      //   herePath: '_id',
      //   therePath: 'members',
      //   operator: '$in'
      // }

    }
  })

  const addDialog = ref(false);
  const limit = ref(20)
  const { h$, pagination, pageRecordCount } = HFind({
    store: mbrStore,
    pause: computed(() => !item.value._id),
    params,
    immediate: true,
    limit,
    // fetchPages: 10,
  })

  // DISABLED: This automatic sync was causing people to be added to all groups
  // The original logic was flawed and would automatically add people to groups
  // based on inconsistencies between group.members and person.inGroups arrays.
  // This has been disabled to prevent unintended group memberships.

  const columns = computed(() => {
    return [
      {
        label: 'Member',
        name: 'owner',
        component: DefaultChip,
        attrs: (row) => {
          return {
            modelValue: row,
            backupNamePath: 'email',
            defaultName: row.email
          }
        }
      },
      {
        name: 'email',
        label: 'Email',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: _get(row, 'email')
            }
          }
        }
      },
      {
        name: 'phone',
        label: 'Phone',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: _get(row, 'phone.number.national', row.phone)
            }
          }
        }
      },
      {
        name: 'pay',
        label: 'Pay',
        component: CamsChip,
        attrs: (row) => {
          return {
            personId: row._id,
            orgId: item.value.org
          }
        }
      },
      {
        name: 'login',
        label: 'Has Login',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: !!row?.login ? 'Yes' : 'No'
            }
          }
        }
      },
      {
        name: 'edit',
        label: '',
        component: MemberEditButton,
        attrs: (row) => {
          return {
            modelValue: row,
            groupId: item.value?._id
          }
        }
      }
    ].map(a => {
      return {
        label: a.name,
        sortable: false,
        align: 'left',
        field: a.name,
        ...a
      };
    })
  });
</script>

<style lang="scss" scoped>
  .__dc {
    border-radius: 12px;
    box-shadow: 0 0 18px -9px rgba(0, 0, 0, .5);
    background: white;
  }

  .__tc {
    position: relative;
  }
</style>
