<template>
  <div>
    <div class="row">

      <div class="col-12 col-md-3 q-pa-md __mf">
        <div class="row q-py-md">
          <div class="__mt">{{ modelValue?.name }}</div>
        </div>

        <div v-for="(key, i) in Object.keys(items).filter(a=>!items[a].off)" :key="`key-${i}`">
          <q-btn flat rounded :class="`_fw text-left text-weight-bold bg-${tab === key ? 'p1' : 'transparent'}`" no-caps @click="setTab(key)">
            <div class="_fw text-left">{{items[key].label}}</div>
          </q-btn>
        </div>
      </div>

      <div class="col-12 col-md-9 q-pa-md">

        <component
            v-on="items[tab].on"
            :is="items[tab].component"
            v-bind="items[tab].attrs"
        ></component>

      </div>
    </div>
  </div>
</template>

<script setup>
  import GroupDetails from 'src/components/groups/forms/GroupDetails.vue';
  import MembersList from 'src/components/groups/cards/MembersList.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {canU} from 'src/utils/ucans/client-auth';
  import { loginPerson } from 'src/stores/utils/login';
  import {useRoute, useRouter} from 'vue-router';
  const route = useRoute();
  const router = useRouter();

  const { login } = loginPerson()


  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    org: { required: true },
    modelValue: { required: false }
  })

  const tab = ref('people');

  const canEdit = ref({ ok: false });
  watch(() => props.modelValue, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      const requiredCapabilities = [[`orgs:${nv.org}`, 'WRITE'], ['groups', 'WRITE'], ['orgs', 'WRITE']];
      // if (login.value?._id && (nv.members || []).includes(login.value._id)) canEdit.value = { ok: true };
      canEdit.value = await canU({ requiredCapabilities, or: true, login })
    }
  }, { immediate: true })

  const items = computed(() => {
    return {
      'people': {
        off: !props.modelValue?._id,
        label: 'People',
        component: MembersList,
        attrs: {
          modelValue: props.modelValue,
          org: props.org
        }
      },
      'details': {
        label: 'Details',
        component: GroupDetails,
        on: {
          'update:model-value': (val) => {
            emit('update:model-value', val);
          }
        },
        attrs: {
          modelValue: props.modelValue,
          canEdit: canEdit.value?.ok ?  canEdit.value : !props.modelValue?._id ? { ok: true } : { ok: false },
          orgId: props.org?._id || props.org || props.modelValue?.org
        }
      }
    }
  })
  const setTab = (t) => {
    router.push({...route, query: {...route.query, membersTab: t }});
    // window.history.pushState({}, '', href);
    tab.value = t;
  }

  onMounted(() => {
    if(!props.modelValue) tab.value = 'details';
    if(route.query.membersTab) tab.value = route.query.membersTab;
  })
</script>

<style lang="scss" scoped>
  .__mt {
    font-weight: 700;
    font-size: 1rem;
    padding: 3px 8px;
    border-radius: 3px;
    background: var(--q-ir-grey-4);
    color: black;
  }
  .__mf {
    border-right: solid .3px rgba(0,0,0,.6);
  }

  @media screen and (max-width: 1023px){
    .__mf {
      border-right: none;
      border-bottom: solid .3px rgba(0,0,0,.6);
    }
  }
</style>
